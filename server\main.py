"""
FastAPI 服务端主入口
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer
from contextlib import asynccontextmanager

from database import init_db
from api import tasks, commissions, users


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    init_db()
    yield
    # 关闭时清理资源


app = FastAPI(
    title="电商爬虫佣金系统",
    description="""
    ## 功能概述

    支持任务分配和佣金计算的电商爬虫系统，提供完整的用户管理、任务分配、佣金结算功能。

    ## 主要特性

    - 🔐 **用户认证**: JWT Token 认证，支持用户注册、登录
    - 📋 **任务管理**: 自动任务分配，支持多平台爬虫任务
    - 💰 **佣金系统**: 实时佣金计算，推荐返佣机制
    - 🏪 **多平台支持**: 支持天猫、京东等主流电商平台
    - 📊 **数据统计**: 详细的任务和收益统计

    ## 认证说明

    大部分 API 需要 JWT Token 认证，请先调用登录接口获取 token，然后在请求头中添加：
    ```
    Authorization: Bearer <your_token>
    ```
    """,
    version="1.0.0",
    contact={
        "name": "电商爬虫系统开发团队",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    terms_of_service="https://ecommerce-crawler.com/terms",
    openapi_tags=[
        {
            "name": "用户",
            "description": "用户注册、登录、信息管理相关接口",
        },
        {
            "name": "任务",
            "description": "任务拉取、提交、状态查询相关接口",
        },
        {
            "name": "佣金",
            "description": "佣金查询、结算、推荐返佣相关接口",
        },
        {
            "name": "系统",
            "description": "系统健康检查、配置管理相关接口",
        },
    ],
    lifespan=lifespan,
    # 配置 OpenAPI 安全方案
    openapi_components={
        "securitySchemes": {
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",
                "description": "输入 JWT Token，格式：Bearer <token>"
            }
        }
    }
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(tasks.router, prefix="/api/tasks", tags=["任务"])
app.include_router(commissions.router, prefix="/api/commissions", tags=["佣金"])
app.include_router(users.router, prefix="/api/users", tags=["用户"])


@app.get("/", tags=["系统"])
async def root():
    """
    ## 系统根路径

    返回系统基本信息和欢迎消息。

    **响应示例:**
    ```json
    {
        "message": "电商爬虫佣金系统 API",
        "version": "1.0.0",
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }
    ```
    """
    return {
        "message": "电商爬虫佣金系统 API",
        "version": "1.0.0",
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }


@app.get("/health", tags=["系统"])
async def health_check():
    """
    ## 健康检查接口

    用于检查系统运行状态，通常用于负载均衡器和监控系统。

    **响应示例:**
    ```json
    {
        "status": "healthy",
        "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
    """
    from datetime import datetime
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
